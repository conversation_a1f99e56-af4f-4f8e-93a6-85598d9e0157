{"name": "bazar-buddy", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@react-navigation/native": "^7.1.17", "@react-navigation/native-stack": "^7.3.25", "dayjs": "^1.11.13", "expo": "~53.0.20", "expo-linking": "~7.1.7", "expo-localization": "~16.1.6", "expo-print": "~14.1.4", "expo-sharing": "~13.1.5", "expo-speech": "~13.1.7", "expo-sqlite": "~15.2.14", "expo-status-bar": "~2.2.3", "express": "^5.1.0", "i18next": "^25.3.4", "node-cache": "^5.1.2", "node-fetch": "^2.7.0", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.6.1", "react-native": "0.79.5", "react-native-paper": "^5.14.5", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}