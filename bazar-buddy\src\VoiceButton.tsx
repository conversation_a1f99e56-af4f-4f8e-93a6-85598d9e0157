import React, { useEffect, useRef, useState } from 'react';
import { Platform, Alert } from 'react-native';
import { IconButton } from 'react-native-paper';
import { parseCommand, Intent } from './voice';

export default function VoiceButton({ onIntent }: { onIntent: (i: Intent) => void }) {
  const [listening, setListening] = useState(false);
  const recRef = useRef<any>(null);

  useEffect(() => {
    if (Platform.OS === 'web') {
      // Web Speech Recognition
      const SR = (window as any).webkitSpeechRecognition || (window as any).SpeechRecognition;
      if (!SR) return;
      const rec = new SR();
      rec.lang = 'en-US';
      rec.continuous = false;
      rec.interimResults = false;
      rec.onresult = (e: any) => {
        const text = e.results?.[0]?.[0]?.transcript as string;
        console.log('Web voice input:', text);
        processVoiceInput(text);
        setListening(false);
      };
      rec.onend = () => setListening(false);
      rec.onerror = (e: any) => {
        console.error('Web speech recognition error:', e);
        setListening(false);
      };
      recRef.current = rec;
    }
    // Removed mobile voice setup - using text input instead
  }, []);
  function processVoiceInput(text: string) {
    console.log('Processing voice input:', text);
    const intent = parseCommand(text);
    console.log('Parsed intent:', intent);
    if (intent) {
      onIntent(intent);
    } else {
      Alert.alert(
        'Voice Input',
        `You said: "${text}"\n\nNo command recognized. Try:\n• "Add milk"\n• "چینی شامل کرو"\n• "Best price for bread"\n• "Mark milk as bought"\n• "Share to WhatsApp"`
      );
    }
  }

  function showVoiceInput() {
    Alert.prompt(
      '🎤 Voice Commands',
      'Type your voice command:',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Process',
          onPress: (text) => {
            if (text && text.trim()) {
              processVoiceInput(text.trim());
            }
          }
        }
      ],
      'plain-text',
      '',
      'default'
    );
  }

  function toggle() {
    if (Platform.OS === 'web') {
      const rec = recRef.current;
      if (!rec) {
        showVoiceInput();
        return;
      }
      if (!listening) {
        setListening(true);
        try {
          rec.lang = 'en-US';
          rec.start();
        } catch (error) {
          console.error('Failed to start web speech recognition:', error);
          setListening(false);
          showVoiceInput();
        }
      } else {
        rec.stop();
        setListening(false);
      }
    } else {
      // Mobile: Use text input (reliable and works everywhere)
      showVoiceInput();
    }
  }

  return (
    <IconButton
      icon={listening ? 'microphone' : 'microphone-outline'}
      onPress={toggle}
      iconColor={listening ? '#f44336' : undefined}
    />
  );
}

