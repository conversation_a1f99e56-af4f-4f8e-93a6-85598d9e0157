import { MD3DarkTheme, MD3LightTheme, configureFonts } from 'react-native-paper';
import { useColorScheme } from 'react-native';

export const lightTheme = {
  ...MD3LightTheme,
  colors: { ...MD3LightTheme.colors },
  fonts: configureFonts({})
};

export const darkTheme = {
  ...MD3DarkTheme,
  colors: { ...MD3DarkTheme.colors },
  fonts: configureFonts({})
};

export function useSystemTheme() {
  const scheme = useColorScheme();
  return scheme === 'dark' ? darkTheme : lightTheme;
}

