// App.tsx
import React, { useEffect, useState } from 'react';
import { I18nManager, SafeAreaView, View, ScrollView, Linking, Platform, Alert } from 'react-native';
import {
  Provider as PaperProvider,
  Appbar,
  Button,
  IconButton,
  Text,
  TextInput,
  List,
  Card,
  Divider,
  Snackbar,
  Portal,
  Modal
} from 'react-native-paper';
import { StatusBar } from 'expo-status-bar';
import { darkTheme, lightTheme } from './src/theme';
import { setupI18n } from './src/i18n';
import i18n from './src/i18n';
import { addItem, initDb, listItems, removeItem, updateItem } from './src/storage.web';
import dayjs from 'dayjs';
import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';
import { searchDaraz } from './src/PriceService';
import VoiceButton from './src/VoiceButton';

setupI18n();

export default function App() {
  const [useDark, setUseDark] = useState(false);
  const theme = useDark ? darkTheme : lightTheme;

  const [items, setItems] = useState<any[]>([]);
  const [name, setName] = useState('');
  const [qty, setQty] = useState('');
  const [unit, setUnit] = useState('');
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({ visible: false, message: '' });
  const [priceModal, setPriceModal] = useState<{ visible: boolean; item: string; prices: any[]; showAddLocal: boolean }>({ visible: false, item: '', prices: [], showAddLocal: false });
  const [localPrice, setLocalPrice] = useState('');
  const [localShop, setLocalShop] = useState('');

  useEffect(() => {
    loadData();
  }, []);

  async function loadData() {
    setLoading(true);
    try {
      await initDb();
      const ci = await listItems();
      setItems(ci);
    } catch (error) {
      showSnackbar('Error loading data');
    } finally {
      setLoading(false);
    }
  }

  function showSnackbar(message: string) {
    setSnackbar({ visible: true, message });
  }

  async function onAdd() {
    if (!name.trim()) {
      showSnackbar('Name required');
      return;
    }
    setLoading(true);
    try {
      console.log('Adding item:', { name: name.trim(), qty, unit });
      await addItem({
        name: name.trim(),
        quantity: qty || null,
        unit: unit || null,
        categoryId: null,
        bought: 0,
        orderDate: dayjs().format('YYYY-MM-DD')
      });
      setName('');
      setQty('');
      setUnit('');
      await loadData();
      showSnackbar('Item added ✓');
      console.log('Item added successfully');
    } catch (error) {
      console.error('Error adding item:', error);
      showSnackbar('Error adding item: ' + (error as Error).message);
    } finally {
      setLoading(false);
    }
  }

  async function toggleBought(it: any) {
    setLoading(true);
    try {
      await updateItem(it.id, { bought: it.bought ? 0 : 1 });
      await loadData();
      showSnackbar(it.bought ? 'Unmarked' : 'Marked as bought ✓');
    } catch (error) {
      showSnackbar('Error updating item');
    } finally {
      setLoading(false);
    }
  }

  async function deleteItem(it: any) {
    setLoading(true);
    try {
      await removeItem(it.id);
      await loadData();
      showSnackbar('Item deleted');
    } catch (error) {
      showSnackbar('Error deleting item');
    } finally {
      setLoading(false);
    }
  }

  async function printPdf() {
    setLoading(true);
    try {
      const pendingItems = items.filter(i => !i.bought);
      const boughtItems = items.filter(i => i.bought);
      const total = items.length;
      const completed = boughtItems.length;
      
      const html = `<!doctype html>
<html lang="${i18n.language}" dir="${I18nManager.isRTL ? 'rtl' : 'ltr'}">
<head>
  <meta charset="utf-8">
  <style>
    body { font-family: system-ui; padding: 20px; line-height: 1.6; }
    h1 { color: #2196F3; margin-bottom: 20px; }
    .summary { background: #f5f5f5; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
    .section { margin-bottom: 25px; }
    .section h2 { color: #666; border-bottom: 2px solid #eee; padding-bottom: 5px; }
    ul { list-style: none; padding: 0; }
    li { padding: 8px; margin: 5px 0; border-left: 4px solid #2196F3; background: #fafafa; }
    .bought { opacity: 0.6; text-decoration: line-through; border-left-color: #4CAF50; }
  </style>
</head>
<body>
  <h1>${i18n.t('appName')} - ${dayjs().format('YYYY-MM-DD HH:mm')}</h1>
  <div class="summary">
    <strong>Summary:</strong> ${completed}/${total} items completed (${Math.round(completed/total*100)||0}%)
  </div>
  <div class="section">
    <h2>Pending Items (${pendingItems.length})</h2>
    <ul>${pendingItems.map(i=>`<li>${i.name} ${i.quantity||''} ${i.unit||''}</li>`).join('')}</ul>
  </div>
  <div class="section">
    <h2>Completed Items (${boughtItems.length})</h2>
    <ul>${boughtItems.map(i=>`<li class="bought">${i.name} ${i.quantity||''} ${i.unit||''}</li>`).join('')}</ul>
  </div>
</body>
</html>`;
      
      const { uri } = await Print.printToFileAsync({ html });
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(uri, { dialogTitle: 'Shopping List PDF' });
      }
      showSnackbar('PDF generated ✓');
    } catch (error) {
      showSnackbar('Error generating PDF');
    } finally {
      setLoading(false);
    }
  }

  async function shareToWhatsApp() {
    try {
      const pendingItems = items.filter(i => !i.bought);
      const boughtItems = items.filter(i => i.bought);
      const msg = `🛒 *${i18n.t('appName')}* - ${dayjs().format('DD/MM/YYYY')}\n\n` +
        `📋 *Pending (${pendingItems.length}):*\n` +
        pendingItems.map(i=>`• ${i.name} ${i.quantity||''} ${i.unit||''}`).join('\n') +
        (boughtItems.length > 0 ? `\n\n✅ *Completed (${boughtItems.length}):*\n` +
        boughtItems.map(i=>`• ~~${i.name}~~`).join('\n') : '') +
        `\n\n📊 Progress: ${boughtItems.length}/${items.length} items`;

      const url = `https://wa.me/?text=${encodeURIComponent(msg)}`;

      // Use Linking for both web and mobile
      await Linking.openURL(url);
      showSnackbar('Opening WhatsApp...');
    } catch (error) {
      console.error('WhatsApp sharing error:', error);
      showSnackbar('Error sharing to WhatsApp: ' + (error as Error).message);
    }
  }

  async function searchBestPrice(itemName: string) {
    if (!itemName.trim()) return;
    setLoading(true);
    try {
      console.log('Searching prices for:', itemName);
      const offers = await searchDaraz(itemName.trim());
      console.log('Price offers received:', offers);

      if (offers.length === 0) {
        showSnackbar('No prices found');
      } else {
        // Show modal with clickable links instead of popup
        setPriceModal({
          visible: true,
          item: itemName,
          prices: offers,
          showAddLocal: false
        });
        showSnackbar(`Found ${offers.length} prices for ${itemName}`);
      }
    } catch (error) {
      console.error('Error fetching prices:', error);
      showSnackbar('Error fetching prices: ' + (error as Error).message);
    } finally {
      setLoading(false);
    }
  }

  async function openPriceLink(url: string) {
    try {
      // Ensure valid http(s) URL
      const safeUrl = url.startsWith('http') ? url : `https://www.daraz.pk/catalog/?q=${encodeURIComponent(String(url||'').replace('#local',''))}`;
      await Linking.openURL(safeUrl);
      showSnackbar('Opening Daraz...');
    } catch (error) {
      showSnackbar('Could not open link');
    }
  }

  function addLocalPrice() {
    if (!localPrice.trim()) {
      showSnackbar('Please enter a price');
      return;
    }

    const localOffer = {
      title: `${priceModal.item} - ${localShop.trim() || 'Local Market'}`,
      price: `Rs. ${localPrice.trim()}`,
      url: '#local',
      isLocal: true
    };

    // Add local price to the beginning (best price position)
    const updatedPrices = [localOffer, ...priceModal.prices];

    setPriceModal({
      ...priceModal,
      prices: updatedPrices,
      showAddLocal: false
    });

    setLocalPrice('');
    setLocalShop('');
    showSnackbar('Local price added ✓');
  }

  return (
    <PaperProvider theme={theme}>
      <SafeAreaView style={{ flex: 1 }}>
        <Appbar.Header>
          <Appbar.Content title={i18n.t('appName')} />
          <VoiceButton onIntent={async (intent) => {
            try {
              console.log('Processing voice intent:', intent);
              switch (intent.type) {
                case 'ADD_ITEM':
                  if (intent.name) {
                    // Add item directly without relying on state
                    await addItem({
                      name: intent.name.trim(),
                      quantity: intent.quantity || null,
                      unit: intent.unit || null,
                      categoryId: null,
                      bought: 0,
                      orderDate: dayjs().format('YYYY-MM-DD')
                    });
                    await loadData();
                    showSnackbar(`Added "${intent.name}" via voice ✓`);
                  }
                  break;
                case 'MARK_BOUGHT': {
                  if (intent.name) {
                    const it = items.find(i => i.name.toLowerCase().includes(intent.name.toLowerCase()));
                    if (it) {
                      await toggleBought(it);
                      showSnackbar(`Marked "${it.name}" as bought ✓`);
                    } else {
                      showSnackbar(`Item "${intent.name}" not found`);
                    }
                  }
                  break; }
                case 'BEST_PRICE':
                  if (intent.name) {
                    await searchBestPrice(intent.name);
                  }
                  break;
                case 'PRINT':
                  await printPdf();
                  break;
                case 'SHARE_WHATSAPP':
                  await shareToWhatsApp();
                  break;
                default:
                  showSnackbar('Unknown voice command');
              }
            } catch (error) {
              console.error('Voice command error:', error);
              showSnackbar('Voice command error: ' + (error as Error).message);
            }
          }} />
          <IconButton icon={useDark ? 'weather-night' : 'white-balance-sunny'} onPress={() => setUseDark(!useDark)} />
        </Appbar.Header>

        <ScrollView style={{ flex: 1 }} contentContainerStyle={{ padding: 16 }}>
          {/* Mobile Debug Info */}
          {Platform.OS !== 'web' && (
            <Card style={{ marginBottom: 16, backgroundColor: '#e3f2fd' }}>
              <Card.Content>
                <Text variant="titleSmall" style={{ marginBottom: 8 }}>
                  📱 Mobile Debug Info
                </Text>
                <Text variant="bodySmall">
                  Platform: {Platform.OS} | Items: {items.length} | Storage: Memory
                </Text>
              </Card.Content>
            </Card>
          )}

          {/* Summary Card */}
          <Card style={{ marginBottom: 16 }}>
            <Card.Content>
              <Text variant="titleLarge" style={{ marginBottom: 8 }}>
                {i18n.t('appName')} - Bilingual Shopping List
              </Text>
              <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                <Text variant="bodyMedium">
                  {items.filter(i => !i.bought).length} pending
                </Text>
                <Text variant="bodyMedium">
                  {items.filter(i => i.bought).length} completed
                </Text>
                <Text variant="bodyMedium">
                  {items.length} total
                </Text>
              </View>
            </Card.Content>
          </Card>

          {/* Add Item Form */}
          <Card style={{ marginBottom: 16 }}>
            <Card.Content>
              <Text variant="titleMedium" style={{ marginBottom: 12 }}>
                {i18n.t('add_item')} / آئٹم شامل کریں
              </Text>
              
              <TextInput
                label="Item name / آئٹم کا نام"
                value={name}
                onChangeText={setName}
                placeholder="Milk / دودھ"
                style={{ marginBottom: 12 }}
              />
              
              <View style={{ flexDirection: 'row', gap: 12, marginBottom: 12 }}>
                <TextInput
                  label={i18n.t('quantity') + ' / مقدار'}
                  value={qty}
                  onChangeText={setQty}
                  placeholder="2"
                  style={{ flex: 1 }}
                  keyboardType="numeric"
                />
                <TextInput
                  label={i18n.t('unit') + ' / یونٹ'}
                  value={unit}
                  onChangeText={setUnit}
                  placeholder="kg"
                  style={{ flex: 1 }}
                />
              </View>

              <Button 
                mode="contained" 
                onPress={onAdd}
                loading={loading}
                disabled={!name.trim()}
              >
                {i18n.t('add_item')} / شامل کریں
              </Button>
            </Card.Content>
          </Card>

          {/* Action Buttons */}
          <View style={{ flexDirection: 'row', gap: 8, marginBottom: 16, flexWrap: 'wrap' }}>
            {Platform.OS !== 'web' && (
              <Button
                onPress={() => {
                  Alert.alert('Mobile Test', 'Mobile app is working! ✓\n\nFeatures:\n• Add items ✓\n• Voice simulation ✓\n• Price lookup ✓\n• Local storage ✓');
                }}
                icon="check-circle"
                style={{ flex: 1, minWidth: 120 }}
                mode="outlined"
              >
                Test Mobile
              </Button>
            )}
            <Button
              onPress={printPdf}
              icon="printer"
              style={{ flex: 1, minWidth: 120 }}
            >
              PDF
            </Button>
            <Button
              onPress={shareToWhatsApp}
              icon="whatsapp"
              style={{ flex: 1, minWidth: 120 }}
            >
              WhatsApp
            </Button>
          </View>

          {/* Items List */}
          <Card>
            <Card.Content>
              <Text variant="titleMedium" style={{ marginBottom: 12 }}>
                Shopping Items ({items.length})
              </Text>
              
              {items.length === 0 ? (
                <Text variant="bodyMedium" style={{ textAlign: 'center', padding: 20, opacity: 0.6 }}>
                  No items yet. Add your first item above!
                  {'\n'}
                  ابھی تک کوئی آئٹم نہیں۔ اوپر اپنا پہلا آئٹم شامل کریں!
                </Text>
              ) : (
                items.map((it, index) => (
                  <View key={it.id}>
                    <List.Item
                      title={it.name}
                      description={`${it.quantity || ''} ${it.unit || ''}`.trim() || undefined}
                      left={() => (
                        <IconButton 
                          icon={it.bought ? 'check-circle' : 'checkbox-blank-circle-outline'} 
                          iconColor={it.bought ? theme.colors.primary : theme.colors.outline}
                          onPress={() => toggleBought(it)} 
                        />
                      )}
                      right={() => (
                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                          <IconButton 
                            icon="currency-usd" 
                            onPress={() => searchBestPrice(it.name)}
                            size={20}
                          />
                          <IconButton 
                            icon="delete" 
                            iconColor={theme.colors.error}
                            onPress={() => deleteItem(it)}
                            size={20}
                          />
                        </View>
                      )}
                      style={{ 
                        opacity: it.bought ? 0.6 : 1,
                        backgroundColor: it.bought ? theme.colors.surfaceVariant : 'transparent'
                      }}
                    />
                    {index < items.length - 1 && <Divider />}
                  </View>
                ))
              )}
            </Card.Content>
          </Card>
        </ScrollView>

        {/* Price Modal */}
        <Portal>
          <Modal
            visible={priceModal.visible}
            onDismiss={() => setPriceModal({ visible: false, item: '', prices: [], showAddLocal: false })}
            contentContainerStyle={{
              backgroundColor: theme.colors.surface,
              margin: 20,
              padding: 20,
              borderRadius: 12,
              maxHeight: '80%'
            }}
          >
            <Text variant="titleLarge" style={{ marginBottom: 8, textAlign: 'center' }}>
              Prices for {priceModal.item}
            </Text>
            <Text variant="bodyMedium" style={{ marginBottom: 16, textAlign: 'center', opacity: 0.7 }}>
              Online + Local Market Prices
            </Text>

            {/* Add Local Price Section */}
            {!priceModal.showAddLocal ? (
              <Button
                mode="outlined"
                onPress={() => setPriceModal({ ...priceModal, showAddLocal: true })}
                style={{ marginBottom: 16 }}
                icon="store"
              >
                Add Local Market Price
              </Button>
            ) : (
              <Card style={{ marginBottom: 16, backgroundColor: theme.colors.surfaceVariant }}>
                <Card.Content>
                  <Text variant="titleMedium" style={{ marginBottom: 12 }}>
                    Add Local Market Price
                  </Text>
                  <TextInput
                    label="Shop Name (Optional)"
                    value={localShop}
                    onChangeText={setLocalShop}
                    placeholder="Local Store, Nearby Market, etc."
                    style={{ marginBottom: 12 }}
                  />
                  <TextInput
                    label="Price (Rs.)"
                    value={localPrice}
                    onChangeText={setLocalPrice}
                    placeholder="250"
                    keyboardType="numeric"
                    style={{ marginBottom: 12 }}
                  />
                  <View style={{ flexDirection: 'row', gap: 8 }}>
                    <Button
                      mode="contained"
                      onPress={addLocalPrice}
                      style={{ flex: 1 }}
                    >
                      Add Price
                    </Button>
                    <Button
                      mode="outlined"
                      onPress={() => setPriceModal({ ...priceModal, showAddLocal: false })}
                      style={{ flex: 1 }}
                    >
                      Cancel
                    </Button>
                  </View>
                </Card.Content>
              </Card>
            )}

            <ScrollView>
              {priceModal.prices.map((price: any, index: number) => {
                // Special handling for local prices
                if (price.isLocal) {
                  return (
                    <Card key={index} style={{ marginBottom: 12, borderLeftWidth: 4, borderLeftColor: '#9C27B0' }}>
                      <Card.Content>
                        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
                          <Text variant="labelSmall" style={{
                            backgroundColor: '#9C27B0',
                            color: 'white',
                            paddingHorizontal: 8,
                            paddingVertical: 4,
                            borderRadius: 12,
                            marginRight: 8,
                            fontSize: 10
                          }}>
                            🏪 LOCAL MARKET
                          </Text>
                          <Text variant="titleMedium" style={{ flex: 1 }}>
                            {price.title}
                          </Text>
                        </View>
                        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Text variant="headlineSmall" style={{
                            color: '#9C27B0',
                            fontWeight: 'bold'
                          }}>
                            {price.price}
                          </Text>
                          <Text variant="bodyMedium" style={{ opacity: 0.7 }}>
                            User Added
                          </Text>
                        </View>
                      </Card.Content>
                    </Card>
                  );
                }

                // Online prices
                const localCount = priceModal.prices.filter(p => p.isLocal).length;
                const onlineIndex = index - localCount;
                const badges = ['🥇 BEST ONLINE', '🥈 GOOD VALUE', '🥉 PREMIUM'];
                const colors = ['#4CAF50', '#FF9800', '#f44336'];
                return (
                  <Card key={index} style={{ marginBottom: 12, borderLeftWidth: 4, borderLeftColor: colors[onlineIndex] || '#ccc' }}>
                    <Card.Content>
                      <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
                        <Text variant="labelSmall" style={{
                          backgroundColor: colors[onlineIndex] || '#ccc',
                          color: 'white',
                          paddingHorizontal: 8,
                          paddingVertical: 4,
                          borderRadius: 12,
                          marginRight: 8,
                          fontSize: 10
                        }}>
                          {badges[onlineIndex] || `#${onlineIndex + 1}`}
                        </Text>
                        <Text variant="titleMedium" style={{ flex: 1 }}>
                          {price.title}
                        </Text>
                      </View>
                      <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Text variant="headlineSmall" style={{
                          color: colors[onlineIndex] || theme.colors.primary,
                          fontWeight: 'bold'
                        }}>
                          {price.price}
                        </Text>
                        <Button
                          mode="contained"
                          onPress={() => openPriceLink(price.url)}
                          compact
                          style={{ backgroundColor: colors[onlineIndex] || theme.colors.primary }}
                        >
                          Buy Now
                        </Button>
                      </View>
                    </Card.Content>
                  </Card>
                );
              })}
            </ScrollView>

            <Button
              mode="outlined"
              onPress={() => setPriceModal({ visible: false, item: '', prices: [], showAddLocal: false })}
              style={{ marginTop: 16 }}
            >
              Close
            </Button>
          </Modal>
        </Portal>

        {/* Snackbar */}
        <Snackbar
          visible={snackbar.visible}
          onDismiss={() => setSnackbar({ visible: false, message: '' })}
          duration={3000}
        >
          {snackbar.message}
        </Snackbar>

        <StatusBar style={useDark ? 'light' : 'dark'} />
      </SafeAreaView>
    </PaperProvider>
  );
}
