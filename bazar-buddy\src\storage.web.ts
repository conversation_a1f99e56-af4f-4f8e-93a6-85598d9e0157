export type Category = { id: number; name: string; parentId?: number | null };
export type Item = {
  id: number;
  name: string;
  categoryId?: number | null;
  subcategoryId?: number | null;
  quantity?: string | null;
  unit?: string | null;
  orderDate?: string | null; // ISO date
  bought: number; // 0/1
};

const WEB_KEY = 'bazar-buddy-store-v1';

type Store = { categories: Category[]; items: Item[]; nextCatId: number; nextItemId: number };

// Mobile-compatible storage
let memoryStore: Store | null = null;

function loadWeb(): Store {
  // Use memory store for mobile
  if (memoryStore) return memoryStore;

  // Try localStorage for web
  if (typeof localStorage !== 'undefined') {
    try {
      const raw = localStorage.getItem(WEB_KEY);
      if (raw) {
        const parsed = JSON.parse(raw) as Store;
        memoryStore = parsed;
        return parsed;
      }
    } catch {}
  }

  // Default store
  const defaultStore = { categories: [], items: [], nextCatId: 1, nextItemId: 1 };
  memoryStore = defaultStore;
  return defaultStore;
}

function saveWeb(s: Store) {
  memoryStore = s;
  // Also save to localStorage if available (web)
  if (typeof localStorage !== 'undefined') {
    try {
      localStorage.setItem(WEB_KEY, JSON.stringify(s));
    } catch {}
  }
}

export async function initDb() {
  let s = loadWeb();
  if (s.categories.length === 0) {
    const cats = [
      'Groceries','Produce','Dairy','Meat','Bakery','Beverages','Household','Cleaning','Pharmacy','Baby','Personal Care','Electronics'
    ];
    s.categories = cats.map((name, idx) => ({ id: idx + 1, name, parentId: null }));
    s.nextCatId = cats.length + 1;
    saveWeb(s);
  }
}

export async function listCategories(parentId?: number | null) {
  const s = loadWeb();
  return (parentId == null ? s.categories.filter(c => c.parentId == null) : s.categories.filter(c => c.parentId === parentId)).sort((a,b)=>a.name.localeCompare(b.name));
}

export async function addCategory(name: string, parentId?: number | null) {
  const s = loadWeb();
  const id = s.nextCatId++;
  s.categories.push({ id, name, parentId: parentId ?? null });
  saveWeb(s);
  return id;
}

export async function addItem(i: Omit<Item, 'id'>) {
  const s = loadWeb();
  const id = s.nextItemId++;
  s.items.push({ id, ...i });
  saveWeb(s);
  return id;
}

export async function listItems() {
  const s = loadWeb();
  return [...s.items].sort((a,b)=> (a.bought - b.bought) || a.name.localeCompare(b.name));
}

export async function updateItem(id: number, fields: Partial<Item>) {
  const s = loadWeb();
  const idx = s.items.findIndex(i=>i.id===id);
  if (idx>=0) {
    s.items[idx] = { ...s.items[idx], ...fields } as Item;
    saveWeb(s);
  }
}

export async function removeItem(id: number) {
  const s = loadWeb();
  s.items = s.items.filter(i=>i.id!==id);
  saveWeb(s);
}

