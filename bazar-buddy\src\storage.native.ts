import * as SQLite from 'expo-sqlite';

export type Category = { id: number; name: string; parentId?: number | null };
export type Item = {
  id: number;
  name: string;
  categoryId?: number | null;
  subcategoryId?: number | null;
  quantity?: string | null;
  unit?: string | null;
  orderDate?: string | null; // ISO date
  bought: number; // 0/1
};

const db = SQLite.openDatabaseSync('bazar.sqlite');

export async function initDb() {
  await db.execAsync(`
    PRAGMA journal_mode = wal;
    CREATE TABLE IF NOT EXISTS categories (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      parentId INTEGER
    );
    CREATE TABLE IF NOT EXISTS items (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      categoryId INTEGER,
      subcategoryId INTEGER,
      quantity TEXT,
      unit TEXT,
      orderDate TEXT,
      bought INTEGER DEFAULT 0
    );
  `);
  const res = await db.getFirstAsync<{ count: number }>(`SELECT COUNT(*) as count FROM categories`);
  if (!res || res.count === 0) {
    const cats = [
      'Groceries','Produce','Dairy','Meat','Bakery','Beverages','Household','Cleaning','Pharmacy','Baby','Personal Care','Electronics'
    ];
    for (const c of cats) await db.runAsync(`INSERT INTO categories (name) VALUES (?)`, [c]);
  }
}

export async function listCategories(parentId?: number | null) {
  if (parentId == null) return db.getAllAsync<Category>(`SELECT * FROM categories WHERE parentId IS NULL ORDER BY name`);
  return db.getAllAsync<Category>(`SELECT * FROM categories WHERE parentId = ? ORDER BY name`, [parentId]);
}

export async function addCategory(name: string, parentId?: number | null) {
  const res = await db.runAsync(`INSERT INTO categories (name, parentId) VALUES (?, ?)`, [name, parentId ?? null]);
  return res.lastInsertRowId as number;
}

export async function addItem(i: Omit<Item, 'id'>) {
  const res = await db.runAsync(
    `INSERT INTO items (name, categoryId, subcategoryId, quantity, unit, orderDate, bought) VALUES (?,?,?,?,?,?,?)`,
    [i.name, i.categoryId ?? null, i.subcategoryId ?? null, i.quantity ?? null, i.unit ?? null, i.orderDate ?? null, i.bought ?? 0]
  );
  return res.lastInsertRowId as number;
}

export async function listItems() {
  return db.getAllAsync<Item>(`SELECT * FROM items ORDER BY bought, name`);
}

export async function updateItem(id: number, fields: Partial<Item>) {
  const keys = Object.keys(fields) as (keyof Item)[];
  if (keys.length === 0) return;
  const sets = keys.map(k => `${k} = ?`).join(', ');
  const values = keys.map(k => (fields as any)[k]);
  await db.runAsync(`UPDATE items SET ${sets} WHERE id = ?`, [...values, id]);
}

export async function removeItem(id: number) {
  await db.runAsync(`DELETE FROM items WHERE id = ?`, [id]);
}

