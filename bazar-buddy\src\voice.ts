// Minimal, free rule-based intent parsing for Urdu/English; STT wiring later.
export type Intent =
  | { type: 'ADD_ITEM'; name: string; quantity?: string; unit?: string; category?: string; date?: string }
  | { type: 'MARK_BOUGHT'; name: string }
  | { type: 'MARK_UNBOUGHT'; name: string }
  | { type: 'CREATE_CATEGORY'; name: string }
  | { type: 'BEST_PRICE'; name: string }
  | { type: 'PRINT' }
  | { type: 'SHARE_WHATSAPP' };

export function parseCommand(textRaw: string): Intent | null {
  console.log('Parsing command:', textRaw);
  const text = textRaw.trim();
  // English patterns
  if (/^print/i.test(text)) return { type: 'PRINT' };
  if (/^share( to)? whatsapp/i.test(text)) return { type: 'SHARE_WHATSAPP' };
  if (/^best price for/i.test(text)) return { type: 'BEST_PRICE', name: text.replace(/^best price for/i, '').trim() };
  if (/^add /i.test(text)) {
    // naive: "add 2 kg sugar to groceries for friday"
    const m = text.match(/^add\s+(.+)$/i);
    if (m) return { type: 'ADD_ITEM', name: m[1] };
  }
  if (/^mark .* as bought/i.test(text)) return { type: 'MARK_BOUGHT', name: text.replace(/^mark\s*|\s*as bought/gi, '').trim() } as any;
  if (/^mark .* as not bought/i.test(text)) return { type: 'MARK_UNBOUGHT', name: text.replace(/^mark\s*|\s*as not bought/gi, '').trim() } as any;
  if (/^create category/i.test(text)) return { type: 'CREATE_CATEGORY', name: text.replace(/^create category/i, '').trim() };

  // Urdu patterns (simple)
  if (/پرنٹ/i.test(text)) return { type: 'PRINT' };
  if (/واٹس ?ایپ/i.test(text) && /شیئر/i.test(text)) return { type: 'SHARE_WHATSAPP' };
  if (/بہترین قیمت/i.test(text)) {
    const name = text.replace(/(کی|کے)?\s*بہترین قیمت( بتاؤ| بتا دو|)/i, '').trim();
    return { type: 'BEST_PRICE', name };
  }
  // Urdu add patterns - more flexible
  if (/شامل|ایڈ|add/i.test(text)) {
    // Extract item name after "شامل کرو" or similar
    let name = text.replace(/(شامل\s*کرو|ایڈ\s*کرو|add)/i, '').trim();
    if (name) return { type: 'ADD_ITEM', name };
  }
  if (/خریدا ہوا|bought/i.test(text) && /مارک|mark/i.test(text)) {
    const name = text.replace(/(خریدا ہوا مارک کرو|mark.*bought)/i, '').trim();
    if (name) return { type: 'MARK_BOUGHT', name };
  }
  if (/نہ خریدا ہوا|خریدا نہیں|not bought/i.test(text)) {
    const name = text.replace(/(نہ خریدا ہوا|خریدا نہیں|not bought) مارک کرو/i, '').trim();
    if (name) return { type: 'MARK_UNBOUGHT', name };
  }
  if (/نئی|new/i.test(text) && /(category|کیٹیگری)/i.test(text)) {
    const name = text.replace(/(نئی\s*|new\s*)(category|کیٹیگری)\s*/i, '').trim();
    if (name) return { type: 'CREATE_CATEGORY', name };
  }

  console.log('No intent matched for:', text);
  return null;
}

