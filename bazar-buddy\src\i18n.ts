import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import * as Localization from 'expo-localization';
import { I18nManager } from 'react-native';

const en = {
  "appName": "Bazar Buddy",
  "lists": "Lists",
  "groceries": "Groceries",
  "add_item": "Add item",
  "quantity": "Quantity",
  "unit": "Unit",
  "date": "Date",
  "categories": "Categories",
  "subcategories": "Subcategories",
  "search_best_price": "Search Best Price",
  "share_whatsapp": "Share to WhatsApp",
  "print_pdf": "Print / Save PDF",
  "bought": "Bought",
  "not_bought": "Not Bought",
  "settings": "Settings",
  "language": "Language",
  "theme": "Theme",
  "auto_remove_bought": "Auto-remove when bought",
  "dark": "Dark",
  "light": "Light",
  "system": "System",
  "ur": "Urdu",
  "en": "English"
};

const ur = {
  "appName": "بازار بڈی",
  "lists": "فہرستیں",
  "groceries": "گروسری",
  "add_item": "آئٹم شامل کریں",
  "quantity": "مقدار",
  "unit": "یونٹ",
  "date": "تاریخ",
  "categories": "زمرے",
  "subcategories": "ذیلی زمرے",
  "search_best_price": "بہترین قیمت تلاش کریں",
  "share_whatsapp": "واٹس ایپ پر شیئر کریں",
  "print_pdf": "پی ڈی ایف پرنٹ/محفوظ کریں",
  "bought": "خریدا ہوا",
  "not_bought": "نہ خریدا ہوا",
  "settings": "سیٹنگز",
  "language": "زبان",
  "theme": "تھیم",
  "auto_remove_bought": "خریدنے پر خودکار ہٹائیں",
  "dark": "ڈارک",
  "light": "لائٹ",
  "system": "سسٹم",
  "ur": "اردو",
  "en": "انگریزی"
};

const resources = { en: { translation: en }, ur: { translation: ur } };

const deviceLang = (Localization.getLocales?.()[0]?.languageCode || 'en') as 'en' | 'ur';

export function setupI18n() {
  const lng = deviceLang === 'ur' ? 'ur' : 'en';
  const isRTL = lng === 'ur';
  if (I18nManager.isRTL !== isRTL) {
    I18nManager.allowRTL(isRTL);
    I18nManager.forceRTL(isRTL);
  }
  if (!i18n.isInitialized) {
    i18n.use(initReactI18next).init({
      resources,
      lng,
      fallbackLng: 'en',
      interpolation: { escapeValue: false },
    });
  }
  return i18n;
}

export default i18n;

