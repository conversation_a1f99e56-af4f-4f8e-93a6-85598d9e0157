import express from 'express';
import fetch from 'node-fetch';
import NodeCache from 'node-cache';

const app = express();
const cache = new NodeCache({ stdTTL: 3600 });

app.get('/prices', async (req, res) => {
  const q = String(req.query.q||'').trim();
  if (!q) return res.json([]);
  const key = `daraz:${q.toLowerCase()}`;
  const cached = cache.get(key);
  if (cached) return res.json(cached);
  try {
    const url = `https://www.daraz.pk/catalog/?q=${encodeURIComponent(q)}`;
    const r = await fetch(url, { headers: { 'Accept-Language': 'en-US,en;q=0.9,ur;q=0.8' } });
    const html = await r.text();
    const offers: any[] = [];
    const itemRegex = /\{"name":"([^"]+)","price":"([^"]+)"/g;
    let m: RegExpExecArray | null;
    while ((m = itemRegex.exec(html)) && offers.length < 3) {
      offers.push({ title: m[1], price: m[2], url });
    }
    cache.set(key, offers);
    res.json(offers);
  } catch (e) {
    res.json([]);
  }
});

const port = process.env.PORT || 3030;
app.listen(port, () => console.log('Price server on', port));

