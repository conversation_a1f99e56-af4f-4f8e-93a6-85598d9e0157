# 🎤 BAZAR BUDDY VOICE COMMANDS GUIDE

## 📱 HOW TO USE VOICE COMMANDS

### **Web (Browser)**
1. Click the **🎤 microphone button** in the app header
2. **Allow microphone access** when prompted
3. **Speak clearly** in English or Urdu
4. Wait for the command to be processed

### **Mobile (Expo Go)**
1. Install **Expo Go** from App Store/Play Store
2. Scan the QR code shown in terminal
3. Click the **🎤 microphone button** in the app
4. Speak your command

---

## 🗣️ SUPPORTED VOICE COMMANDS

### **ADD ITEMS**
| English | Urdu | Result |
|---------|------|--------|
| "Add milk" | "دودھ شامل کرو" | Adds milk to list |
| "Add 2 kg sugar" | "چینی دو کلو شامل کرو" | Adds sugar with quantity |
| "Add bread" | "روٹی شامل کرو" | Adds bread to list |
| "Add chicken" | "چکن شامل کرو" | Adds chicken to list |
| "Add rice" | "چاول شامل کرو" | Adds rice to list |

### **MARK ITEMS AS BOUGHT**
| English | Urdu | Result |
|---------|------|--------|
| "Mark milk as bought" | "دودھ خریدا ہوا مارک کرو" | Marks milk as purchased |
| "Mark sugar as bought" | "چینی خریدا ہوا مارک کرو" | Marks sugar as purchased |
| "Mark bread as bought" | "روٹی خریدا ہوا مارک کرو" | Marks bread as purchased |

### **PRICE LOOKUP**
| English | Urdu | Result |
|---------|------|--------|
| "Best price for milk" | "دودھ کی بہترین قیمت" | Shows online + local prices for milk |
| "Price for sugar" | "چینی کی قیمت" | Shows sugar prices with local option |
| "Best price for bread" | "روٹی کی بہترین قیمت" | Shows bread prices + add local price |

### **SHARING & EXPORT**
| English | Urdu | Result |
|---------|------|--------|
| "Share to WhatsApp" | "واٹس ایپ پر شیئر کرو" | Opens WhatsApp with list |
| "Print the list" | "لسٹ پرنٹ کرو" | Generates PDF |
| "Print" | "پرنٹ" | Generates PDF |

### **LOCAL MARKET PRICES**
| Action | How To | Result |
|--------|--------|--------|
| Add local price | Click $ icon → "Add Local Market Price" | Compare with online prices |
| Enter shop name | Type "Local Store" or "Nearby Market" | Shows as source |
| Enter price | Type "250" (without Rs.) | Adds Rs. automatically |
| Compare prices | View all prices together | Best deal highlighted |

---

## 📋 COMMON GROCERY ITEMS (Voice Ready)

### **Dairy & Eggs**
- Milk / دودھ
- Yogurt / دہی  
- Cheese / پنیر
- Butter / مکھن
- Eggs / انڈے

### **Vegetables**
- Onions / پیاز
- Tomatoes / ٹماٹر
- Potatoes / آلو
- Carrots / گاجر
- Spinach / پالک

### **Fruits**
- Apples / سیب
- Bananas / کیلے
- Oranges / سنترے
- Mangoes / آم
- Grapes / انگور

### **Grains & Staples**
- Rice / چاول
- Wheat flour / آٹا
- Sugar / چینی
- Salt / نمک
- Oil / تیل

### **Meat & Protein**
- Chicken / چکن
- Beef / گائے کا گوشت
- Fish / مچھلی
- Lentils / دال

### **Bakery**
- Bread / روٹی
- Biscuits / بسکٹ
- Cake / کیک

---

## 🎯 VOICE COMMAND EXAMPLES

### **Complete Shopping Session**
1. **"Add 2 liters milk"** → Adds milk with quantity
2. **"چینی ایک کلو شامل کرو"** → Adds 1kg sugar
3. **"Best price for milk"** → Shows online + local market prices
4. **Add local price** → Click "Add Local Market Price" in price modal
5. **"Mark milk as bought"** → Checks off milk
6. **"Share to WhatsApp"** → Sends list to WhatsApp

### **Quick Add Session**
1. **"Add bread"**
2. **"Add eggs"** 
3. **"روٹی شامل کرو"**
4. **"انڈے شامل کرو"**

---

## 🔧 TROUBLESHOOTING

### **Voice Not Working?**
1. **Check microphone permissions** in browser
2. **Speak clearly** and wait for processing
3. **Try shorter commands** first
4. **Use the manual buttons** as backup

### **Commands Not Recognized?**
1. **Speak slowly** and clearly
2. **Use exact phrases** from the guide above
3. **Try English** if Urdu doesn't work
4. **Check microphone** is working

### **Price Lookup Issues?**
1. **Ensure price server is running** (http://localhost:3030)
2. **Check internet connection**
3. **Try simpler item names**

---

## 📱 MOBILE SETUP

### **Step 1: Install Expo Go**
- **Android**: Download from Google Play Store
- **iOS**: Download from App Store

### **Step 2: Connect to App**
1. **Scan QR code** shown in terminal
2. **Wait for app to load**
3. **Test voice commands**

### **Step 3: Voice on Mobile**
- **Tap microphone button**
- **Allow microphone access**
- **Speak commands clearly**

---

## 🌟 PRO TIPS

### **Best Voice Practices**
- **Speak clearly** at normal pace
- **Use simple item names** (milk, not "organic whole milk")
- **Wait 2 seconds** between commands
- **Try both languages** if one doesn't work

### **Efficient Shopping**
1. **Add all items first** with voice
2. **Check prices** for expensive items
3. **Mark bought** as you shop
4. **Share final list** to WhatsApp

### **Family Use**
- **Share WhatsApp list** with family members
- **Print PDF** for offline shopping
- **Use both English and Urdu** as needed

---

## 🚀 QUICK START

1. **Open**: http://localhost:8081 in browser
2. **Click**: 🎤 microphone button  
3. **Say**: "Add milk" or "دودھ شامل کرو"
4. **Test**: Price lookup with "Best price for milk"
5. **Share**: "Share to WhatsApp"

**Your bilingual voice-controlled shopping app is ready! 🛒**
